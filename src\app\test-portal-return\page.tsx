'use client'

import { useRouter } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/button'

export default function TestPortalReturn() {
  const router = useRouter()

  const simulatePortalReturn = () => {
    // Simulate returning from Stripe Customer Portal
    router.push('/billing?portal_return=true')
  }

  const simulateUpgradeSuccess = () => {
    // Simulate successful subscription upgrade
    router.push('/billing?success=true&type=subscription')
  }

  return (
    <div className="max-w-2xl mx-auto py-8 px-4">
      <h1 className="text-2xl font-bold mb-6">Test Portal Return Flow</h1>
      
      <div className="space-y-4">
        <div className="p-4 border rounded-lg">
          <h2 className="text-lg font-semibold mb-2">Test Portal Return</h2>
          <p className="text-gray-600 mb-4">
            This simulates a user returning from the Stripe Customer Portal after making changes.
            It should check for recent subscription upgrades and show the success modal if found.
          </p>
          <Button onClick={simulatePortalReturn}>
            Simulate Portal Return
          </Button>
        </div>

        <div className="p-4 border rounded-lg">
          <h2 className="text-lg font-semibold mb-2">Test Direct Success</h2>
          <p className="text-gray-600 mb-4">
            This simulates the existing flow for new subscriptions with success=true parameter.
          </p>
          <Button onClick={simulateUpgradeSuccess}>
            Simulate Direct Success
          </Button>
        </div>
      </div>

      <div className="mt-8 p-4 bg-gray-50 rounded-lg">
        <h3 className="font-semibold mb-2">How to Test:</h3>
        <ol className="list-decimal list-inside space-y-1 text-sm">
          <li>Make sure you have a user account with a subscription</li>
          <li>Create a recent transaction (within 5 minutes) with type 'subscription_upgrade'</li>
          <li>Click "Simulate Portal Return" to test the new flow</li>
          <li>The billing page should detect the portal return and show the upgrade success modal</li>
        </ol>
      </div>
    </div>
  )
}
